#import "LaunchViewController.h"
#import "NetworkManager.h"
#import "CustomTabBarController.h"
#import "LoginViewController.h"
#import "LoginInitModel.h"
#import "AddressDataManager.h"
#import "LocationManager.h"
#import "AppDelegate.h"
#import <CFNetwork/CFNetwork.h>
#import <SystemConfiguration/SystemConfiguration.h>
#import <AFNetworking/AFNetworkReachabilityManager.h>

// === Helper Functions ===
static BOOL NLIsProxyEnabled(void) {
    NSDictionary *proxySettings = (__bridge_transfer NSDictionary *)CFNetworkCopySystemProxySettings();
    if (!proxySettings) { return NO; }
    NSNumber *httpEnable = proxySettings[@"HTTPEnable"];
    NSNumber *httpsEnable = proxySettings[@"HTTPSEnable"];
    NSNumber *socksEnable = proxySettings[@"SOCKSEnable"];
    return (httpEnable.boolValue || httpsEnable.boolValue || socksEnable.boolValue);
}

static BOOL NLIsVPNEnabled(void) {
    NSDictionary *proxySettings = (__bridge_transfer NSDictionary *)CFNetworkCopySystemProxySettings();
    NSDictionary *scoped = proxySettings[@"__SCOPED__"];
    for (NSString *key in scoped.allKeys) {
        if ([key containsString:@"tap"] || [key containsString:@"tun"] || [key containsString:@"ppp"] || [key containsString:@"ipsec"] || [key containsString:@"utun"]) {
            return YES;
        }
    }
    return NO;
}

@interface LaunchViewController ()
@property (nonatomic, strong) AFNetworkReachabilityManager *reachabilityManager;
@property (nonatomic, assign) BOOL hasStartedInitRequest;
@end

@implementation LaunchViewController

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    // 设置基础域名（真实接口）
    [NetworkManager setBaseURL:@"https://nano.paperplane-lending.com/youtoday"];
    // 启动应用时上报位置信息
    [[LocationManager sharedManager] reportLocationInfo];

    // 启动网络状态监听，确保网络可用后再发起请求
    [self startNetworkMonitoring];
    [self startNetworkMonitoring];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // 添加全屏启动背景图
    UIImageView *backgroundImageView = [[UIImageView alloc] initWithFrame:self.view.bounds];
    backgroundImageView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    backgroundImageView.contentMode = UIViewContentModeScaleAspectFill;
    backgroundImageView.image = [UIImage imageNamed:@"launch_bg_image"];
    [self.view addSubview:backgroundImageView];
}

/// 启动网络状态监听
- (void)startNetworkMonitoring {
    // 创建网络状态监听器
    self.reachabilityManager = [AFNetworkReachabilityManager sharedManager];

    __weak typeof(self) weakSelf = self;
    [self.reachabilityManager setReachabilityStatusChangeBlock:^(AFNetworkReachabilityStatus status) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf handleNetworkStatusChange:status];
        });
    }];

    // 开始监听
    [self.reachabilityManager startMonitoring];

    // 立即检查当前网络状态
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [weakSelf handleNetworkStatusChange:weakSelf.reachabilityManager.networkReachabilityStatus];
    });

    // 备用机制：如果3秒后仍未发起请求，则强制发起（避免网络监听失效）
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (!weakSelf.hasStartedInitRequest) {
            NSLog(@"[Launch] 备用机制触发：强制发起初始化请求");
            weakSelf.hasStartedInitRequest = YES;
            [weakSelf requestLoginInitWithRetry:NO];
            [weakSelf.reachabilityManager stopMonitoring];
            weakSelf.reachabilityManager = nil;
        }
    });
}

/// 处理网络状态变化
- (void)handleNetworkStatusChange:(AFNetworkReachabilityStatus)status {
    NSLog(@"[Launch] 网络状态变化: %ld", (long)status);

    // 如果网络可用且还未发起初始化请求
    if ((status == AFNetworkReachabilityStatusReachableViaWiFi || status == AFNetworkReachabilityStatusReachableViaWWAN) && !self.hasStartedInitRequest) {
        self.hasStartedInitRequest = YES;
        NSLog(@"[Launch] 网络可用，开始发起初始化请求");
        [self requestLoginInitWithRetry:NO];

        // 停止网络监听以避免重复请求
        [self.reachabilityManager stopMonitoring];
        self.reachabilityManager = nil;
    }
}

/// 发起登录初始化请求，支持重试一次
- (void)requestLoginInitWithRetry:(BOOL)hasRetried {
    __weak typeof(self) weakSelf = self;

    // ==== 构造初始化参数 ====
    NSString *langCode = [[NSLocale preferredLanguages] firstObject] ?: @"en";
    if (langCode.length > 2) {
        langCode = [[langCode substringToIndex:2] lowercaseString];
    }

    BOOL usingProxy = NLIsProxyEnabled();
    BOOL usingVPN = NLIsVPNEnabled();

    NSDictionary *initParams = @{@"bigger": langCode,
                                 @"ofgwendoline": @(usingProxy),
                                 @"outright": @(usingVPN)};

    [NetworkManager postFormWithAPI:@"Alicia/bigger" params:initParams completion:^(NSDictionary *response, NSError *error) {
        if (!error && [response isKindOfClass:[NSDictionary class]]) {
            // 保存数据到model
            LoginInitModel *model = [[LoginInitModel alloc] initWithDictionary:response];
            NSData *data = [NSKeyedArchiver archivedDataWithRootObject:model requiringSecureCoding:YES error:nil];
            [[NSUserDefaults standardUserDefaults] setObject:data forKey:@"LoginInitModelCache"];

            // ===== 缓存初始化数据供全局使用 =====
            // 保存完整 JSON 到 Documents/login_init_cache.json
            NSString *jsonPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES).firstObject stringByAppendingPathComponent:@"login_init_cache.json"];
            NSData *jsonData = [NSJSONSerialization dataWithJSONObject:response options:0 error:nil];
            [jsonData writeToFile:jsonPath atomically:YES];

            // 提取手机国家区号 topmost 并缓存
            NSString *topmost = response[@"awkward"][@"heardmrs"][@"topmost"];
            if ([topmost isKindOfClass:[NSString class]] && topmost.length > 0) {
                [[NSUserDefaults standardUserDefaults] setObject:topmost forKey:@"country_phone_code"];
            }

            // ===== 缓存隐私协议 URL girlswere，供登录页使用 =====
            NSString *girlswere = response[@"awkward"][@"girlswere"];
            if ([girlswere isKindOfClass:[NSString class]] && girlswere.length > 0) {
                [[NSUserDefaults standardUserDefaults] setObject:girlswere forKey:@"protocol_url"];
            }
            // ===== 解析 everyonecheered 字段，供后续位置权限引导使用 =====
            NSNumber *everyonecheered = response[@"awkward"][@"everyonecheered"];
            if ([everyonecheered respondsToSelector:@selector(integerValue)]) {
                [[NSUserDefaults standardUserDefaults] setObject:everyonecheered forKey:@"show_location_guide"];
            }
            [[NSUserDefaults standardUserDefaults] synchronize];

            // === 使用 fearlessness 字段动态初始化 Facebook SDK ===
            NSDictionary *fbConfig = response[@"awkward"][@"fearlessness"];
            AppDelegate *appDelegate = (AppDelegate *)[UIApplication sharedApplication].delegate;
            if ([fbConfig isKindOfClass:[NSDictionary class]] && [appDelegate respondsToSelector:@selector(configureFacebookSDKWithParameters:)]) {
                [appDelegate configureFacebookSDKWithParameters:fbConfig];
            }

            // 判断 modest 字段并在主线程更新 UI
            dispatch_async(dispatch_get_main_queue(), ^{
                NSNumber *modest = response[@"modest"];
                if ([modest respondsToSelector:@selector(integerValue)] && [modest integerValue] == 0) {
                    // 初始化成功，检查登录状态
                    [self checkLoginStatusAndNavigate];
                } else {
                    NSLog(@"[Launch] ⚠️ 初始化失败，modest: %@", modest);
                }
                } else {
                    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Notice" message:@"Service error, please try again later." preferredStyle:UIAlertControllerStyleAlert];
                    [alert addAction:[UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil]];
                    [weakSelf presentViewController:alert animated:YES completion:nil];
                }
            });
        } else {
            if (!hasRetried) {
                // 10秒后重试一次
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(10 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [weakSelf requestLoginInitWithRetry:YES];
                });
            } else {
                // 两次都失败，弹英文报错
                UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Notice" message:@"Network error, please check your connection and try again." preferredStyle:UIAlertControllerStyleAlert];
                [alert addAction:[UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil]];
                [weakSelf presentViewController:alert animated:YES completion:nil];
            }
        }
    }];
}

- (void)dealloc {
    // 确保停止网络监听
    [self.reachabilityManager stopMonitoring];
    self.reachabilityManager = nil;
    NSLog(@"[Launch] LaunchViewController dealloc");
}

@end

/*

{
  "modest": 0,
  "patted": "success",
  "awkward": {
      "roundwarily": 2,   //  1=默认印度(审核面)   2=菲律宾(用户面)  后续请求放在公参里面
      "heardmrs": {
          "topmost": "63", //手机区号
          "swallow": "http://2.412.163.251/national_flag/ph.png" //国旗logo
      },
  "everyonecheered": 1, //  1=弹出位置引导框   2=不弹 (场景：启动拒绝定位权限时，登陆后首页是否弹出位置引导弹窗，一天一次)
  "girlswere": "http://www.abidu.com", // 隐私协议
  "fearlessness": {
    // 【重要】FaceBook接入：http://47.238.207.2:3031/APP/FaceBook.git  账号：wendang  密码：wendang123
      "eventhe":"fb428921739874998",        // CFBundleURLScheme
      "gotback":"428921739874998",           // FacebookAppID
      "chicken":"PinoyLoan",    // FacebookDisplayName
      "ginger":"1a67e07ccdef7ad26a997dff1c5ab821"     // FacebookClientToke
      }
  }
}

*/

#pragma mark - Login Status Check

/// 检查登录状态并导航到相应页面
- (void)checkLoginStatusAndNavigate {
    NSString *token = [[NSUserDefaults standardUserDefaults] stringForKey:@"token"];

    UIWindow *window = self.view.window;
    if (!window) {
        if (@available(iOS 13.0, *)) {
            for (UIWindowScene *scene in [UIApplication sharedApplication].connectedScenes) {
                if (scene.activationState == UISceneActivationStateForegroundActive) {
                    window = scene.windows.firstObject;
                    break;
                }
            }
        } else {
            window = [UIApplication sharedApplication].keyWindow;
        }
    }

    if (!window) {
        NSLog(@"[Launch] ⚠️ 无法获取 Window，无法导航");
        return;
    }

    if (token && token.length > 0) {
        // 有token，进入主页面
        NSLog(@"[Launch] 检测到token，进入主页面");
        CustomTabBarController *tabBarVC = [[CustomTabBarController alloc] init];
        window.rootViewController = tabBarVC;
        [UIView transitionWithView:window duration:0.3 options:UIViewAnimationOptionTransitionCrossDissolve animations:nil completion:^(BOOL finished) {
            if (finished) {
                // 预加载地址数据，方便后续选择器使用（异步执行，不阻塞UI）
                [[AddressDataManager sharedManager] fetchAddressDataIfNeededWithCompletion:nil];
            }
        }];
    } else {
        // 没有token，进入登录页面
        NSLog(@"[Launch] 未检测到token，进入登录页面");
        LoginViewController *loginVC = [[LoginViewController alloc] init];
        window.rootViewController = loginVC;
        [UIView transitionWithView:window duration:0.3 options:UIViewAnimationOptionTransitionCrossDissolve animations:nil completion:nil];
    }
}
